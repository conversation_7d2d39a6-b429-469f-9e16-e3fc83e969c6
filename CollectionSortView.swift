//
//  CollectionSortView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/12.
//

import SwiftUI

struct CollectionSortSheet: View {
    @Binding var collections: [CollectionItem]
    let onSave: () -> Void
    let onCancel: () -> Void

    var body: some View {
            VStack(spacing: 0) {
                // Header
                ZStack {
                    Text("Sort Collections")
                        .font(.title3)
                        .fontWeight(.semibold)

                    HStack {
                        Button("Cancel") {
                            onCancel()
                        }
                        Spacer()
                        Button("Save") {
                            onSave()
                        }
                    }
                }
                .padding()
                .background(Color(.systemBackground))

                // Sortable List
                List {
                    ForEach(collections, id: \.id) { collection in
                        CollectionSortRow(collection: collection)
                            .listRowInsets(EdgeInsets())
                            .listRowSeparator(.hidden)
                    }
                    .onMove(perform: moveCollections)
                }
                .listStyle(.plain)
                .environment(\.editMode, .constant(.active))
            }
    }

    private func moveCollections(from source: IndexSet, to destination: Int) {
        collections.move(fromOffsets: source, toOffset: destination)
    }
}

struct CollectionSortRow: View {
    let collection: CollectionItem
    @EnvironmentObject private var imageManager: ImageManager

    var body: some View {
        HStack(spacing: 16) {
            // Collection info
            HStack(spacing: 12) {
                // Cover image
                coverImageView
                // Collection details
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(collection.name)
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        Spacer()
                    }

                    Text("\(collection.achievementCount) achievements")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
        .padding(.horizontal)
        .padding(.vertical, 4)
    }

    private var coverImageView: some View {
        Group {
            if !collection.cover.isEmpty {
                CloudImageView(path: collection.cover)
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 50, height: 50)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            } else {
                // Default cover with first achievement image or placeholder
                if let firstTask = collection.taskItems?.first,
                   let coverPath = firstTask.coverImagePath {
                    CloudImageView(path: coverPath)
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 50, height: 50)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                } else {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.tertiarySystemBackground))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: "music.note")
                                .font(.title3)
                                .foregroundColor(.secondary)
                        )
                }
            }
        }
    }
}
#Preview {
    @Previewable @State var collections: [CollectionItem] = [
        CollectionItem(name: "Classical Pieces"),
        CollectionItem(name: "Jazz Collection"),
        CollectionItem(name: "Rock Achievements")
    ]
    CollectionSortSheet(collections: $collections, onSave: {}, onCancel: {})
}
