//
//  TaskDetailView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/1.
//

import SwiftUI
import StoreKit
import ConfettiSwiftUI
import SwiftData
import PhotosUI

struct TaskDetailView: View {
    @Query private var tasks: [TaskItem]
    var task: TaskItem? {
        tasks.first { $0.id == taskID }
    }
    var taskID: PersistentIdentifier
    
    
    @Environment(\.requestReview) var requestReview
    @EnvironmentObject private var ratingManager: RatingManager
    @EnvironmentObject var recordingManager: RecordingManager
    @EnvironmentObject var navManager: NavigationManager
//    @EnvironmentObject var videoManager: VideoManager
    
    @StateObject private var taskListManager: TaskListManager = .shared
    
    @State private var sliderValue: Double = 0.0
    @State private var recordingToDelete: RecordingItem?
//    @State private var videoToDelete: VideoItem?
    @State private var showDeleteConfirmation = false
    @State private var selectedRecording: RecordingItem?
//    @State private var selectedVideo: VideoItem?
    
    @State var showingConfirmation = false
    @State private var showConfetti = 0
    
    @State private var showAddMenu = false
    @State private var isLoading = false
    @State private var addType: AddType? = nil // .audio or .video
    @State private var showDocumentPicker = false
    @State private var showVideoPicker = false
    @State private var pickedVideo: PhotosPickerItem?
    @State private var showImportOptions = false
    
    @State private var showTimeModifySheet = false
    
    enum AddType {
        case audio, video
    }
    
    init(task: TaskItem) {
        self.taskID = task.id
    }
    
    var body: some View {
        detailInfo
            .background(Color(.secondarySystemBackground))
            .toolbar {
                ToolbarItem() {
                    if task?.taskType == .Ongoing {
                        Button(action: {
                            showingConfirmation = true
                        }) {
                            Image(systemName: "checkmark.rectangle.stack.fill")
                        }
                    }
                }
                ToolbarItem() {
                    Menu {
                        Button {
                            showImportOptions = true
                        } label: {
                            Label("Add Recording", systemImage: "microphone.fill")
                        }
                        Button {
                            showTimeModifySheet = true
                        } label: {
                            Label("Add Practice Time", systemImage: "chart.bar.fill")
                        }
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showTimeModifySheet) {
                if let task = task {
                    TimeModifyView(
                        onConfirm: { seconds, date in
                            taskListManager.updatePracticeTime(of: task, at: date, by: seconds)
                            showTimeModifySheet = false
                        }
                    )
                }
            }
            .bottomActionSheet(
                isPresented: $showingConfirmation,
                title: String(localized: "Complete this task?"),
                message: String(localized: "Are you sure you want to mark this task as completed? It will be moved to the Completed list."),
                confirmText: String(localized: "Yes")
            ) {
                showConfetti = 1
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    finishTask()
                }
                ratingManager.recordAction()
            }
            .onDisappear {
                if recordingManager.isPlaying {
                    recordingManager.stopPlayback()
                }
            }
            .confettiCannon(counter: $showConfetti, num: 50)
            .fileImporter(isPresented: $showDocumentPicker, allowedContentTypes: [.audio], allowsMultipleSelection: false) { result in
                switch result {
                case .success(let urls):
                    guard let url = urls.first, let task = task else { return }
                    isLoading = true
                    if addType == .audio {
                        Task {
                            defer {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                                    isLoading = false
                                }
                            }

                            do {
                                try await recordingManager.importRecording(from: url, to: task)
                                print("✅ Successfully imported recording")
                            } catch {
                                print("❌ Failed to import recording: \(error)")
                            }
                        }
                    } else {
                        // 如果不是音频类型，立即重置 loading 状态
                        isLoading = false
                    }
                case .failure(let error):
                    print("❌ File import failed: \(error)")
                    isLoading = false
                }
            }
            .photosPicker(
                isPresented: $showVideoPicker,
                selection: $pickedVideo,
                matching: .videos,
                preferredItemEncoding: .current,
                photoLibrary: .shared()
            )
            .onChange(of: pickedVideo) { oldValue, newValue in
                guard let newValue = newValue, let task = task else {
                    // 如果 newValue 或 task 为 nil，重置状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        isLoading = false
                        pickedVideo = nil
                        showVideoPicker = false
                    }
                    return
                }
                isLoading = true
                Task {
                    defer {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            isLoading = false
                            pickedVideo = nil
                            showVideoPicker = false
                        }
                    }

                    do {
                        // 获取视频创建时间（用于更准确的记录）
                        var creationDate = Date.now
                        if let identifier = newValue.itemIdentifier {
                            let assets = PHAsset.fetchAssets(withLocalIdentifiers: [identifier], options: nil)
                            if let asset = assets.firstObject, let date = asset.creationDate {
                                creationDate = date
                                print("🔍 asset.mediaType: \(asset.mediaType.rawValue)") // 2 表示视频
                                print("📦 supportedContentTypes: \(newValue.supportedContentTypes)")
                            }
                        }
                        
                        if let videoURL = try await newValue.loadTransferable(type: URL.self) {
                            try await recordingManager.extractAudioFromVideo(videoURL: videoURL, to: task, at: creationDate)
                            print("✅ Successfully extracted audio from video")
                        } else if let videoData = try await newValue.loadTransferable(type: Data.self) {
                            let tempURL = FileManager.default.temporaryDirectory
                                .appendingPathComponent("temp_video.mov")
                            
                            // 如果旧文件存在，先删掉
                            if FileManager.default.fileExists(atPath: tempURL.path) {
                                try? FileManager.default.removeItem(at: tempURL)
                            }
                            
                            try videoData.write(to: tempURL)
                            try await recordingManager.extractAudioFromVideo(videoURL: tempURL, to: task, at: creationDate)
                            try? FileManager.default.removeItem(at: tempURL)
                            print("✅ Successfully extracted audio from video (via temp file)")
                        } else {
                            print("❌ Could not load video data")
                        }
                    } catch {
                        isLoading = false
                        print("❌ Failed to extract audio from video: \(error)")
                    }
                }
            }
            .overlay {
                if showImportOptions {
                    ImportOptionsOverlay(
                        isPresented: $showImportOptions,
                        onFileImport: {
                            addType = .audio
                            showDocumentPicker = true
                        },
                        onVideoImport: {
                            addType = .video
                            showVideoPicker = true
                        }
                    )
                }
            }
            .overlay {
                if isLoading {
                    VStack {
                        Spacer()
                        ProgressView("Processing, please wait…")
                            .foregroundColor(.primary)
                            .font(.headline)
                        Spacer()
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(.ultraThinMaterial)
                }
            }
    }
    
    func finishTask () {
        if let task = task {
            taskListManager.finishTask(taskItem: task)
            // Switch to Finished tab when task becomes achievement
            AppGlobalStateManager.shared.selectedListType = .finished
            navManager.popState()
            AppGlobalStateManager.shared.setItemToFocus(task.id)
        }
    }
    
    var detailInfo: some View {
        ScrollableCoverView(
            title: task?.pieceName ?? "Task",
            header: {
                taskHeader
            },
            background: {
                taskBackground
            }
        ) {
            LazyVStack(alignment: .leading, spacing: 0, pinnedViews: []) {
                basicInfo
                    .padding(.top)
                    .padding(.horizontal)
                statisticsSection
                recordingSection
            }
            .padding(.bottom)
        }
    }
        
    @ViewBuilder
    private var taskBackground: some View {
        if let task = task, let imagePath = task.coverImagePath {
            CloudImageView(path: imagePath, refreshTrigger: "\(task.id)")
                .scaledToFill()
        } else {
            // Default gradient background when no cover image
            ZStack {
                Color(hex: "#6991a5")

                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.white.opacity(0.05),
                        Color.black.opacity(0.1)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
        }
    }

    @ViewBuilder
    private var taskHeader: some View {
        VStack(alignment: .leading, spacing: 0) {
            Spacer()
            HStack(alignment: .bottom) {
                VStack(alignment: .leading) {
                    HStack {
                        Text(task?.pieceName ?? "Task")
                            .lineLimit(2)
                            .font(.largeTitle)
                        Spacer()
                    }
                    HStack {
                        Text(task?.composerName ?? "")
                            .font(.subheadline)
                        Spacer()
                    }
                }
                if let task = task {
                    let seconds = getPracticeTime(of: task)
                    if seconds > 0 {
                        let times = getTimeStringPair(of: seconds, decimal: 1)
                        LaurelTimeViewSmall(
                            timeValue: times.time,
                            timeUnit: times.unit
                        )
                    }
                }
            }
        }
        .bold()
        .shadow(color: .black.opacity(0.5), radius: 3)
        .foregroundColor(.white)
        .padding()
    }
    
    @ViewBuilder
    var basicInfo: some View {
        if let task = task {
            VStack {
                HStack(alignment: .top) {
                    InfoBlockDetailView(label: String(localized: "Composer"), value: task.composerName)
                    InfoBlockDetailView(label: String(localized: "Started On"), value: task.beginDate.formatted(.dateTime.year().month().day()))
                }
                .padding(.horizontal)
                .padding(.top)

                if (task.taskType == .Achievement) {
                    HStack(alignment: .top) {
                        InfoBlockDetailView(label: String(localized: "Completed On"), value: task.finishedDate.formatted(.dateTime.year().month().day()))
                        let duration = getDayDifference(from: task.beginDate, to: task.finishedDate)
                        InfoBlockDetailView(label: String(localized: "Total Duration"), value: String(localized: "\(duration) days"))
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
            }
            .background(Color(.systemBackground))
            .cornerRadius(10)
        }
    }
    
    @ViewBuilder
    var recordingSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Recordings")
                Spacer()            }
            .font(.system(size: 22, weight: .medium))
            .padding()
            let recordings = (task?.recordings ?? []).sorted { $0.date > $1.date }
            if recordings.count == 0 {
                EmptyStateView(imageName: "ReadingPiano")
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.horizontal)
            } else {
                LazyVStack(spacing: 0) {
                    ForEach(recordings, id: \.self.id) { recording in
                        RecordingItemView(
                            recording: recording,
                            selectedRecording: $selectedRecording,
                            sliderValue: $sliderValue,
                            showDeleteConfirmation: $showDeleteConfirmation,
                            recordingToDelete: $recordingToDelete
                        )
                        .padding(.vertical, 5)
                        .padding(.horizontal)
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    var statisticsSection: some View {
        if let task = task {
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text("Statistics")
                    Image(systemName: "chevron.right")
                        .font(.callout)
                        .fontWeight(.black)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .font(.system(size: 22, weight: .medium))
                .padding()
                .onTapGesture {
                    navManager.pushState(item: .practiceTimeDetailPage(task: task))
                }

                if task.taskProgress?.count == 0 {
                    EmptyStateView(
                        imageName: "CatPiano",
                        title: String(localized: "No data yet"),
                        subtitle: String(localized: "You don't have any practice data for this piece yet!")
                    )
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.horizontal)
                } else {
                    TaskStatisticsNavigator(task: task)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .padding(.horizontal)
                        .padding(.bottom)
                }
            }
        }
    }
}

struct InfoBlock: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                Text(label)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.leading)
                Text(value)
                    .font(.body)
                    .multilineTextAlignment(.leading)
            }
            Spacer()
        }.padding(0)
    }
}

struct InfoBlockDetailView: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                Text(label)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.leading)
                Text(value)
                    .font(.body)
                    .multilineTextAlignment(.leading)
            }
            Spacer()
        }.padding(.bottom)
    }
}

struct EmptyStateView: View {
    var systemImageName: String = "music.note"
    var imageName: String?
    var title: String = String(localized: "No Recordings Yet")
    var subtitle: String = String(localized: "You haven't recorded this piece yet.")
    
    var body: some View {
        VStack(spacing: 0) {
            if let imageName = imageName {
                Image(imageName)
                    .resizable()
                    .scaledToFit()
                    .frame(maxWidth: 400)
                    .foregroundColor(.gray.opacity(0.5))
            } else {
                Image(systemName: systemImageName)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 80, height: 80)
                    .foregroundColor(.gray.opacity(0.5))
            }
            
            Text(title)
                .font(.custom("Nunito-Bold", size: 20))
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            Text(subtitle)
                .font(.custom("Nunito-Bold", size: 20))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container.mainContext
    let task = TaskItem(
        pieceName: "Nocturne Op. 09 Nr, 01",
        composerName: "Chopin",
        key: .aMinor,
        difficulty: .nine,
        beginDate: Date(),
        taskProgress: []
    )
    modelContext.insert(task)
    return NavigationStack {
        TaskDetailView(task: task)
    }
}

struct ImportOptionsOverlay: View {
    @Binding var isPresented: Bool
    let onFileImport: () -> Void
    let onVideoImport: () -> Void

    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isPresented = false
                    }
                }

            // 悬浮窗
            VStack(spacing: 0) {
                // 标题
                Text("Import Recording")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .padding(.top, 24)
                    .padding(.bottom, 8)

                Text("Choose how you want to import your recording")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 24)

                // 选项按钮
                VStack(spacing: 12) {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPresented = false
                        }
                        onFileImport()
                    }) {
                        HStack {
                            Image(systemName: "folder.fill")
                                .font(.title2)
                                .foregroundColor(.accentColor)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("From Files")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                Text("Import audio files from your device")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPresented = false
                        }
                        onVideoImport()
                    }) {
                        HStack {
                            Image(systemName: "video.fill")
                                .font(.title2)
                                .foregroundColor(.accentColor)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("From Video in Photos")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                Text("Extract audio from videos in your photo library")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 24)
            }
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            .padding(.horizontal, 40)
            .scaleEffect(isPresented ? 1.0 : 0.8)
            .opacity(isPresented ? 1.0 : 0.0)
        }
        .animation(.easeInOut(duration: 0.2), value: isPresented)
    }
}
