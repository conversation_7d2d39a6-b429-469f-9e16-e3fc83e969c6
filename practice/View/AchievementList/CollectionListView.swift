//
//  CollectionListView.swift
//  practice
//
//  Created by Augment Agent on 2024/12/19.
//

import SwiftUI
import SwiftData

struct CollectionListView: View {
    @EnvironmentObject private var collectionManager: CollectionManager
    @EnvironmentObject private var navManager: NavigationManager
    @EnvironmentObject private var imageManager: ImageManager
    @StateObject private var appGlobalStateManager: AppGlobalStateManager = .shared

    @State private var showingCreateCollection = false
    @State private var selectedCollection: CollectionItem?
    @State private var showingDeleteAlert = false
    @State private var showingSortSheet = false
    @State private var sortableCollections: [CollectionItem] = []
    @State private var collections: [CollectionItem] = []
    @State private var isLoading = true
    @State private var forceRefresh = false

    // 用于处理滚动和闪烁效果
    @State private var blinkingCollection: CollectionItem? = nil
    
    var body: some View {
        VStack(spacing: 0) {
            collectionsListView
        }
        .navigationTitle("Achievement")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            if collections.count > 1 {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        prepareSortSheet()
                        showingSortSheet = true
                    } label: {
                        Image(systemName: "text.line.magnify")
                            .imageScale(.large)
                    }
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                // Add button
                Button {
                    showingCreateCollection = true
                } label: {
                    Image(systemName: "plus")
                        .imageScale(.large)
                }
            }
        }
        .sheet(isPresented: $showingCreateCollection, onDismiss: {
            selectedCollection = nil
            Task {
                await loadCollections()
            }
        }) {
            if let selectedCollection = selectedCollection {
                CreateCollectionView(editingCollection: selectedCollection)
            } else {
                CreateCollectionView()
            }
        }
        .sheet(isPresented: $showingSortSheet) {
            CollectionSortSheet(
                collections: $sortableCollections,
                onSave: {
                    applySortOrder()
                },
                onCancel: {
                    showingSortSheet = false
                }
            )
            .interactiveDismissDisabled(true)
        }
        .bottomActionSheet(
            isPresented: $showingDeleteAlert,
            title: String(localized: "Delete Collection"),
            message: String(localized: "Are you sure you want to delete \"\(selectedCollection?.name ?? "")\"? The tasks it includes will not be deleted."),
            confirmText: String(localized: "Delete")
        ) {
            if let collection = selectedCollection {
                collectionManager.deleteCollection(collection)
                selectedCollection = nil
                Task {
                    await loadCollections()
                }
            }
        }
        .onAppear {
            Task {
                await loadCollections()
            }
        }
        .onChange(of: appGlobalStateManager.imageRefreshTrigger) {
            forceRefresh.toggle()
        }
    }
    
    private var collectionsListView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 0) {
                    if collections.isEmpty {
                        emptyStateView
                    } else {
                        ForEach(collections, id: \.id) { collection in
                            CollectionItemView(collection: collection)
                                .background(collection == blinkingCollection ? Color.accentColor.opacity(0.1) : Color.clear)
                                .animation(.easeInOut(duration: 0.3), value: collection == blinkingCollection)
                                .id("\(collection.id)-\(forceRefresh)") // 结合集合ID和刷新标志
                                .contextMenu {
                                    Button {
                                        selectedCollection = collection
                                        showingCreateCollection = true
                                    } label: {
                                        Label("Edit", systemImage: "square.and.pencil")
                                    }

                                    if collection.type == .userCreated {
                                        Button(role: .destructive) {
                                            selectedCollection = collection
                                            showingDeleteAlert = true
                                        } label: {
                                            Label("Delete", systemImage: "trash.fill")
                                        }
                                    }
                                }
                        }
                    }
                }
                .padding(.vertical)
            }
            .onChange(of: collections) {
                if let focusId = appGlobalStateManager.collectionToFocus {
                    handleCollectionFocus(focusId: "\(focusId)-\(forceRefresh)", scrollProxy: proxy)
                }
            }
        }
    }
    
    private var imageWidth: CGFloat {
        let isIPad = UIDevice.current.userInterfaceIdiom == .pad
        let imageWidth = min(UIScreen.main.bounds.width, UIScreen.main.bounds.height)
        if isIPad {
            return imageWidth * 0.5
        } else {
            return imageWidth * 0.8
        }
    }
    
    
    private var emptyStateView: some View {
        VStack(spacing: 0) {
            Image("AchievementPlaceholder")
                .resizable()
                .aspectRatio(contentMode: /*@START_MENU_TOKEN@*/.fill/*@END_MENU_TOKEN@*/)
                .frame(width: imageWidth)

            Text("No Achievements Collection Yet")
                .foregroundColor(.gray)
                .font(.body)
                .bold()
        }
        .frame(maxWidth: .infinity)
        .padding(.top, 60)
    }

    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("Loading Collections...")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.top, 60)
    }

    // MARK: - Data Loading
    private func loadCollections() async {
        isLoading = true

        // Perform the loading on a background queue
        await Task.detached {
            await MainActor.run {
                collectionManager.loadData()
            }
        }.value

        // Update local state on main thread only if data has changed
        await MainActor.run {
            let newCollections = collectionManager.collections

            // Check if collections have actually changed
            if !CollectionItem.arraysAreEqual(collections, newCollections) {
                self.collections = newCollections
            }

            self.isLoading = false
        }
    }



    // MARK: - Sort Helper Methods
    private func prepareSortSheet() {
        sortableCollections = collections
    }

    private func applySortOrder() {
        collectionManager.applyCustomOrder(sortableCollections)
        Task {
            await loadCollections()
        }
        showingSortSheet = false
    }

    /// 处理集合项聚焦逻辑
    /// - Parameters:
    ///   - focusId: 需要聚焦的集合 ID
    ///   - scrollProxy: ScrollViewReader 的代理
    private func handleCollectionFocus(focusId: UUID, scrollProxy: ScrollViewProxy) {
        // 查找对应的集合项
        guard let targetCollection = collections.first(where: { $0.id == focusId }) else {
            return
        }

        // 滚动到目标集合
        withAnimation(.easeInOut(duration: 0.5)) {
            scrollProxy.scrollTo(targetCollection.id, anchor: .center)
        }

        // 延迟执行闪烁效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            withAnimation(.easeInOut(duration: 0.3)) {
                blinkingCollection = targetCollection
            }

            // 闪烁效果持续时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    blinkingCollection = nil
                }

                // 清除聚焦项
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    appGlobalStateManager.clearCollectionToFocus()
                }
            }
        }
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container.mainContext

    CollectionListView()
        .environmentObject(CollectionManager.shared)
        .environmentObject(NavigationManager.shared)
        .environmentObject(ImageManager(modelContext: modelContext))
}
