//
//  CollectionDetailView.swift
//  practice
//
//  Created by Augment Agent on 2024/12/19.
//

import SwiftUI
import SwiftData

struct CollectionDetailView: View {
    let collection: CollectionItem
    
    @EnvironmentObject private var collectionManager: CollectionManager
    @EnvironmentObject private var navManager: NavigationManager
    @EnvironmentObject private var imageManager: ImageManager
    @EnvironmentObject private var recordingManager: RecordingManager
    @StateObject private var appGlobalStateManager: AppGlobalStateManager = .shared
    
    @State private var showingEditCollection = false
    @State private var showingDeleteAlert = false
    @State private var selectedTask: TaskItem?
    @State private var showingTaskDeleteAlert = false
    @State private var showingEditTaskSheet = false
    @State private var showingShareView = false
    @State private var coverData: CoverData?
    @State private var isRendering = false
    @State private var renderProgress: Double = 0.0
    @State private var forceRefresh = true
    
    @AppStorage("cardView") private var cardView = InfoCardViewType.normal
    var displayScale = 3.0
    
    private let cardViewIconMap: [InfoCardViewType: String] = [
        .compact: "rectangle.grid.1x2.fill",
        .gallery: "rectangle.inset.filled",
        .normal: "rectangle.tophalf.filled",
        .album: "square.grid.2x2.fill"
    ]
    
    @ViewBuilder
    private var backgroundView: some View {
        if !collection.cover.isEmpty {
            CloudImageView(path: collection.cover)
                .aspectRatio(contentMode: .fill)
        }
        else {
            ZStack {
                // 基础色（比如亨乐蓝）
                Color(hex: "#6991a5")
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.white.opacity(0.05),
                        Color.black.opacity(0.1)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
        }

    }

    @ViewBuilder
    private var header: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Total Practice Time Display
            Spacer()
            HStack(alignment: .bottom) {
                VStack {
                    HStack {
                        Text(collection.name)
                            .lineLimit(2)
                            .font(.largeTitle)
                        Spacer()
                    }
                    HStack {
                        Text("\(collection.achievementCount) achievements")
                        Spacer()
                    }
                }
                if let tasks = collection.taskItems, !tasks.isEmpty {
                    let totalSeconds = getPracticeTime(of: tasks)
                    if totalSeconds > 0 {
                        let timeData = getTimeStringPair(of: totalSeconds, decimal: 1)
                        LaurelTimeViewSmall(
                            timeValue: timeData.time,
                            timeUnit: timeData.unit
                        )
                    }
                }
            }
        }
        .bold()
        .shadow(color: .black.opacity(0.5), radius: 3)
        .foregroundColor(.white)
        .padding()
    }
    
    var body: some View {
        ScrollableCoverView(
            title: collection.name,
            header: {
                header
            },
            background: {
                backgroundView
            }
        ) {
            VStack(alignment: .leading, spacing: 0) {
                if !collection.desc.isEmpty {
                    Text(collection.desc)
                        .foregroundStyle(.gray)
                        .font(.body)
                        .padding(.top)
                        .padding(.horizontal)
                }
                tasksListView
                    .padding(.top)
                    .bottomActionSheet(
                        isPresented: $showingTaskDeleteAlert,
                        title: String(localized: "Remove Achievement"),
                        message: String(localized: "Are you sure you want to remove \(selectedTask?.pieceName ?? "") from the collection?"),
                        confirmText: String(localized: "Yes")
                    ) {
                        if let task = selectedTask {
                            collectionManager.removeTaskFromCollection(task, collection: collection)
                        }
                    }
            }
            .padding(.bottom)
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack(spacing: 8) {
                    // Share Button
                    Button {
                        Task {
                            await MainActor.run {
                                render {
                                    showingShareView = true
                                }
                            }
                        }
                    } label: {
                        if isRendering {
                            ProgressView(value: renderProgress)
                                .frame(width: 20, height: 20)
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "square.and.arrow.up")
                        }
                    }
                    .disabled(isRendering)
                    
                    Menu {
                        headerMenu
                    } label: {
                        Image(systemName: "square.stack.fill")
                    }
                    
                    Button {
                        showingEditCollection = true
                    } label: {
                        Image(systemName: "square.and.pencil")
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditCollection) {
            CreateCollectionView(editingCollection: collection)
        }
        .sheet(isPresented: $showingEditTaskSheet, onDismiss: {
            selectedTask = nil
        }) {
            sheetContent(for: selectedTask)
        }
        .fullScreenCover(item: $coverData, onDismiss: {
            coverData = nil
        }) { item in
            if let renderedUIImage = item.renderedUIImage {
                SharePreviewFullScreenView(image: renderedUIImage)
            } else {
                ProgressView("Generating preview...")
            }
        }
        .onChange(of: appGlobalStateManager.imageRefreshTrigger) {
            forceRefresh.toggle()
        }
    }

    private func sheetContent(for taskItem: TaskItem?) -> some View {
        NavigationView {
            if let taskItem = taskItem {
                AddNewItemView(taskItem: taskItem) {
                    showingEditTaskSheet = false
                    appGlobalStateManager.triggerImageRefresh()
                }.navigationBarTitleDisplayMode(.inline)
            } else {
                AddNewItemView(
                    onCreate: {
                        showingEditTaskSheet = false
                    }
                ).navigationBarTitleDisplayMode(.inline)
            }
        }
    }

    @ViewBuilder
    private var tasksListView: some View {
        if let tasks = collection.taskItems, !tasks.isEmpty {
            let sortedTasks = tasks.sorted {
                ($0.pieceName).localizedStandardCompare($1.pieceName) == .orderedAscending
            }
            if cardView == .album {
                AlbumGridView(
                    tasks: sortedTasks,
                    onTaskTap: { task in
                        navManager.pushState(item: .taskDetailPage(task: task))
                    },
                    onTaskEdit: { task in
                        selectedTask = task
                        showingEditTaskSheet = true
                    },
                    onTaskRemove: collection.type == .defaultCollection ? nil : { task in
                        selectedTask = task
                        showingTaskDeleteAlert = true
                    }
                )
                .id("album-\(forceRefresh)")
            } else if cardView == .gallery {
                GalleryGridView(
                    tasks: sortedTasks,
                    onTaskTap: { task in
                        navManager.pushState(item: .taskDetailPage(task: task))
                    },
                    onTaskEdit: { task in
                        selectedTask = task
                        showingEditTaskSheet = true
                    },
                    onTaskRemove: collection.type == .defaultCollection ? nil : { task in
                        selectedTask = task
                        showingTaskDeleteAlert = true
                    }
                )
                .id("gallery-\(forceRefresh)")
            } else {
                ForEach(sortedTasks, id: \.id) { task in
                    getInfoView(task: task)
                        .id("\(task.id)-\(forceRefresh)")
                }
            }
        } else {
            emptyStateView
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            Image(systemName: "music.note.list")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Achievements Yet")
                .foregroundColor(.gray)
                .font(.body)
                .bold()
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .listRowSeparator(.hidden)
        .listRowBackground(Color.clear)
    }
    
    @ViewBuilder
    private var headerMenu: some View {
        Picker("", selection: $cardView) {
            ForEach(cardViewType, id: \.self) { type in
                Image(systemName: cardViewIconMap[type] ?? "rectangle.tophalf.filled")
            }
        }.pickerStyle(.segmented)
    }
    
    private func getInfoView(task: TaskItem) -> some View {
        let view: any View = if cardView == .compact {
            InfoCardCompactView(task: task)
        } else {
            InfoCardView(task: task)
        }
        return AnyView(view)
            .onTapGesture {
                navManager.pushState(item: .taskDetailPage(task: task))
            }
            .taskContextMenu(
                task: task,
                onEdit: { task in
                    selectedTask = task
                    showingEditTaskSheet = true
                },
                onRemove: collection.type == .defaultCollection ? nil : { task in
                    selectedTask = task
                    showingTaskDeleteAlert = true
                }
            )
            .padding(.horizontal)
    }
    
    @MainActor
    func render(completion: @escaping () -> Void) {
        guard let tasks = collection.taskItems else {
            completion()
            return
        }

        isRendering = true
        renderProgress = 0.0

        // 预加载所有图片（高质量模式）
        Task {
            let imagePaths = tasks.compactMap { $0.coverImagePath }
            await imageManager.preloadImagesForRendering(paths: imagePaths)

            await MainActor.run {
                self.renderProgress = 0.2
                // 任务数量较多时，使用分段渲染（每批3个任务）
                self.renderInBatchesHighQuality(tasks: tasks) {
                    self.isRendering = false
                    self.renderProgress = 1.0
                    completion()
                }
            }
        }
    }

    @MainActor
    private func renderInBatchesHighQuality(tasks: [TaskItem], completion: @escaping () -> Void) {
        let batchSize = 4
        var renderedImages: [UIImage] = []

        // 首先渲染头部（背景、标题、描述）
        renderHeaderHighQuality { headerImage in
            if let headerImage = headerImage {
                renderedImages.append(headerImage)
            }

            // 然后分批渲染任务
            self.renderTaskBatchesHighQuality(tasks: tasks, batchSize: batchSize, renderedImages: &renderedImages) { taskImages in
                // 渲染底部
                self.renderFooterHighQuality { footerImage in
                    var finalImages = taskImages
                    if let footerImage = footerImage {
                        finalImages.append(footerImage)
                    }

                    // 使用高质量合成
                    if let combinedImage = self.combineImagesHighQuality(finalImages) {
                        self.coverData = CoverData(renderedUIImage: combinedImage)
                        print("✅ high quality batch render success: \(combinedImage.size), total batches: \(finalImages.count)")
                        completion()
                    } else {
                        print("❌ failed to combine high quality images")
                        completion()
                    }
                }
            }
        }
    }

    // MARK: - Batch Rendering Helper Methods
    @MainActor
    private func renderHeaderHighQuality(completion: @escaping (UIImage?) -> Void) {
        let headerView = VStack(alignment: .leading, spacing: 0) {
            backgroundView
                .frame(width: 500, height: collection.cover.isEmpty ? 300 : nil, alignment: .center)
                .clipped()
                .overlay {
                    header
                        .padding(.horizontal)
                }
            if !collection.desc.isEmpty {
                Text(collection.desc)
                    .foregroundStyle(.gray)
                    .font(.body)
                    .padding(.top)
                    .padding(.horizontal)
                    .padding(.horizontal)
            }
        }
        .frame(width: 500)
        .background(Color.white)
        .environmentObject(imageManager)

        let renderer = ImageRenderer(content: headerView)
        renderer.scale = displayScale // 保持原始质量

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let image = renderer.uiImage {
                completion(image)
            } else {
                // 如果高质量渲染失败，尝试降级
                renderer.scale = displayScale * 0.8
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    completion(renderer.uiImage)
                }
            }
        }
    }

    @MainActor
    private func renderTaskBatchesHighQuality(tasks: [TaskItem], batchSize: Int, renderedImages: inout [UIImage], completion: @escaping ([UIImage]) -> Void) {
        var allImages = renderedImages
        let batches = tasks.chunked(into: batchSize)
        var currentBatchIndex = 0
        let totalBatches = batches.count

        func renderNextBatch() {
            guard currentBatchIndex < batches.count else {
                completion(allImages)
                return
            }

            // 更新进度 (0.2 到 0.9 之间)
            let progress = 0.2 + (Double(currentBatchIndex) / Double(totalBatches)) * 0.7
            renderProgress = progress

            let batch = batches[currentBatchIndex]
            renderTaskBatchHighQuality(tasks: batch) { batchImage in
                if let batchImage = batchImage {
                    allImages.append(batchImage)
                }
                currentBatchIndex += 1

                // 强制内存清理
                autoreleasepool {
                    // 添加短暂延迟以释放内存
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                        renderNextBatch()
                    }
                }
            }
        }

        renderNextBatch()
    }

    
    @MainActor
    private func renderTaskBatchHighQuality(tasks: [TaskItem], completion: @escaping (UIImage?) -> Void) {
        // 预加载这批任务的图片
        preloadImagesForTasks(tasks)

        let batchView = VStack(alignment: .leading, spacing: 0) {
            if cardView == .album {
                LazyVGrid(columns: [
                    GridItem(.fixed(225), spacing: 15, alignment: .top),
                    GridItem(.fixed(225), spacing: 15, alignment: .top)
                ], spacing: 0) {
                    ForEach(tasks, id: \.self) { task in
                        TaskCardView(task: task, width: 225, syncRenderImage: true)
                    }
                }
                .padding(.top)
            } else {
                ForEach(tasks, id: \.id) { task in
                    if cardView == .gallery {
                        InfoCardGalleryView(task: task, width: 470, syncRenderImage: true)
                            .padding(.horizontal)
                            .padding(.top)
                    } else if cardView == .normal {
                        InfoCardView(task: task, syncRenderImage: true)
                            .padding(.horizontal)
                            .padding(.top)
                    } else {
                        InfoCardCompactView(task: task, syncRenderImage: true)
                            .padding(.horizontal)
                            .padding(.top)
                    }
                }
            }
        }
        .frame(width: 500)
        .background(Color.white)
        .environmentObject(imageManager)

        let renderer = ImageRenderer(content: batchView)
        renderer.scale = displayScale // 保持原始质量

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            if let image = renderer.uiImage {
                completion(image)
            } else {
                // 如果高质量渲染失败，尝试降级
                print("⚠️ High quality batch render failed, trying reduced scale...")
                renderer.scale = displayScale * 0.75
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    completion(renderer.uiImage)
                }
            }
        }
    }

    @MainActor
    private func renderFooterHighQuality(completion: @escaping (UIImage?) -> Void) {
        let footerView = VStack {
            ShareImageFooter()
        }
        .padding(.bottom)
        .frame(width: 500)
        .background(Color.white)
        .environmentObject(imageManager)

        let renderer = ImageRenderer(content: footerView)
        renderer.scale = displayScale // 保持原始质量

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let image = renderer.uiImage {
                completion(image)
            } else {
                // 降级处理
                renderer.scale = displayScale * 0.8
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    completion(renderer.uiImage)
                }
            }
        }
    }

    private func preloadImagesForTasks(_ tasks: [TaskItem]) {
        for task in tasks {
            if let path = task.coverImagePath {
                // 预加载图片到缓存
                _ = imageManager.loadImage(path: path)
            }
        }
    }

    private func combineImagesHighQuality(_ images: [UIImage]) -> UIImage? {
        guard !images.isEmpty else { return nil }

        // 确保所有图片都使用统一的宽度（500）
        let targetWidth: CGFloat = 500

        // 标准化所有图片的宽度
        let normalizedImages = images.compactMap { normalizeImageWidth($0, targetWidth: targetWidth) }
        guard !normalizedImages.isEmpty else { return nil }

        let totalHeight = normalizedImages.reduce(0) { $0 + $1.size.height }

        let format = UIGraphicsImageRendererFormat()
        format.scale = displayScale // 使用原始缩放保持质量
        format.opaque = true // 设置为不透明，避免灰色背景
        format.preferredRange = .standard

        let renderer = UIGraphicsImageRenderer(size: CGSize(width: targetWidth, height: totalHeight), format: format)

        return renderer.image { context in
            // 设置白色背景
            context.cgContext.setFillColor(UIColor.white.cgColor)
            context.cgContext.fill(CGRect(x: 0, y: 0, width: targetWidth, height: totalHeight))

            // 设置高质量渲染
            context.cgContext.interpolationQuality = .high
            context.cgContext.setShouldAntialias(true)
            context.cgContext.setAllowsAntialiasing(true)

            var currentY: CGFloat = 0

            for image in normalizedImages {
                // 绘制标准化后的图片
                let rect = CGRect(x: 0, y: currentY, width: targetWidth, height: image.size.height)
                image.draw(in: rect)
                currentY += image.size.height
            }
        }
    }

    // 标准化图片宽度的辅助函数
    private func normalizeImageWidth(_ image: UIImage, targetWidth: CGFloat) -> UIImage? {
        // 如果图片宽度已经是目标宽度，直接返回
        if abs(image.size.width - targetWidth) < 1.0 {
            return image
        }

        // 计算新的高度，保持宽高比
        let aspectRatio = image.size.height / image.size.width
        let targetHeight = targetWidth * aspectRatio

        let format = UIGraphicsImageRendererFormat()
        format.scale = image.scale
        format.opaque = true

        let renderer = UIGraphicsImageRenderer(size: CGSize(width: targetWidth, height: targetHeight), format: format)

        return renderer.image { context in
            // 设置白色背景
            context.cgContext.setFillColor(UIColor.white.cgColor)
            context.cgContext.fill(CGRect(x: 0, y: 0, width: targetWidth, height: targetHeight))

            // 绘制图片
            image.draw(in: CGRect(x: 0, y: 0, width: targetWidth, height: targetHeight))
        }
    }
}

// MARK: - Task Context Menu ViewModifier
struct TaskContextMenuModifier: ViewModifier {
    let task: TaskItem
    let onEdit: (TaskItem) -> Void
    let onRemove: ((TaskItem) -> Void)?

    func body(content: Content) -> some View {
        content
            .contextMenu {
                Button {
                    onEdit(task)
                } label: {
                    Label("Edit", systemImage: "square.and.pencil")
                }

                // 条件展示 Remove 按钮
                if let onRemove = onRemove {
                    Button(role: .destructive) {
                        onRemove(task)
                    } label: {
                        Label("Remove", systemImage: "minus.circle")
                    }
                }
            }
    }
}

extension View {
    func taskContextMenu(
        task: TaskItem,
        onEdit: @escaping (TaskItem) -> Void,
        onRemove: ((TaskItem) -> Void)? = nil
    ) -> some View {
        modifier(TaskContextMenuModifier(task: task, onEdit: onEdit, onRemove: onRemove))
    }
}

// MARK: - Array Extension for Chunking
extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

#Preview {
    let collection = CollectionItem(name: "Chopin Collection", desc: "this is a description", type: .userCreated)
    collection.taskItems = [
        TaskItem(pieceName: "Nocturne Op. 9 No. 1", composerName: "Chopin", key: .eFlatMajor, difficulty: .five, beginDate: Date()),
        TaskItem(pieceName: "Fantaisie-Impromptu", composerName: "Chopin", key: .cSharpMinor, difficulty: .eight, beginDate: Date()),
        TaskItem(pieceName: "Ballade No. 1", composerName: "Chopin", key: .gMinor, difficulty: .nine, beginDate: Date()),
        TaskItem(pieceName: "Etude Op. 10 No. 4", composerName: "Chopin", key: .cSharpMinor, difficulty: .eight, beginDate: Date()),
        TaskItem(pieceName: "Prelude Op. 28 No. 15", composerName: "Chopin", key: .dFlatMajor, difficulty: .six, beginDate: Date()),
        TaskItem(pieceName: "Waltz Op. 64 No. 2", composerName: "Chopin", key: .cSharpMinor, difficulty: .seven, beginDate: Date())
    ]
    let modelContext = TaskItemSchemaV1.container.mainContext

    return NavigationStack {
        CollectionDetailView(collection: collection)
    }
    .environmentObject(CollectionManager.shared)
    .environmentObject(NavigationManager.shared)
    .environmentObject(ImageManager(modelContext: modelContext))
    .environmentObject(RecordingManager(modelContext: modelContext))
}
