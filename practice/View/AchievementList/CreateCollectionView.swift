//
//  CreateCollectionView.swift
//  practice
//
//  Created by Augment Agent on 2024/12/19.
//

import SwiftUI
import PhotosUI

struct CreateCollectionView: View {
    @EnvironmentObject private var collectionManager: CollectionManager
    @EnvironmentObject private var imageManager: ImageManager
    @Environment(\.dismiss) private var dismiss
    
    // Edit mode properties
    let editingCollection: CollectionItem?
    
    // Form state
    @State private var collectionName: String = ""
    @State private var collectionDescription: String = ""
    @State private var selectedTasks: [TaskItem] = []
    @State private var selectedImage: PhotosPickerItem?
    @State private var coverImage: UIImage?
    @State private var originalCoverImagePath: String?
    @State private var isCoverImageModified: Bool = false
    @State private var searchText: String = ""
    
    // UI state
    @State private var showingImagePicker = false
    
    init(editingCollection: CollectionItem? = nil) {
        self.editingCollection = editingCollection
    }
    
    var body: some View {
        VStack(spacing: 0) {
            ZStack {
                Text(editingCollection == nil ? "Add New Collection" : "Edit Collection")
                    .font(.title3)
                    .fontWeight(.semibold)
                
                HStack {
                    Button("Cancel") {
                        cancelChanges()
                    }
                    Spacer()
                    Button(editingCollection == nil ? "Create" : "Save") {
                        saveCollection()
                    }
                    .disabled(collectionName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            // Form Content
            ScrollView {
                VStack(spacing: 24) {
                    // Collection Name Section
                    collectionNameSection
                        .padding(.horizontal)
                    
                    // Cover Image Section
                    coverImageSection
                        .padding(.horizontal)
                    // Task Selection Section
                    if let editingCollection = editingCollection, editingCollection.type == .defaultCollection {
                        defaultSelectionSection
                    } else {
                        taskSelectionSection
                    }
                }
                .padding(.vertical)
            }
        }
        .onAppear {
            setupForEditing()
        }
        .onChange(of: selectedImage) {
            Task {
                if let imageData = try await selectedImage?.loadTransferable(type: Data.self),
                   let uiImage = UIImage(data: imageData) {
                    // 处理图片尺寸
                    let resizedImage = imageManager.resizeImage(uiImage, maxDimension: 5000)

                    // 在UI上立即显示图片（但不保存到模型）
                    coverImage = resizedImage
                    // 标记图片已被修改
                    isCoverImageModified = true
                }
            }
        }
    }
    
    private var collectionNameSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Collection Name")
                .font(.headline)
                .foregroundColor(.primary)
            
            TextField("", text: $collectionName)
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
            Text("Collection Description")
                .font(.headline)
                .foregroundColor(.primary)
            
            TextField("", text: $collectionDescription)
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
        }
    }
    
    private var coverImageSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
//                TODO: offer some system default image
                Text("Cover Image")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            VStack(spacing: 0) {
                PhotosPicker(
                    selection: $selectedImage,
                    matching: .images,
                    photoLibrary: .shared()
                ) {
                    if let coverImage = coverImage {
                        // full width with 3:2 aspect ratio
                        Image(uiImage: coverImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(maxWidth: .infinity, maxHeight: 160)
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                            .overlay(alignment: .topTrailing) {
                                Button(action: {
                                    self.coverImage = nil
                                    selectedImage = nil
                                    // 标记图片已被修改（删除也是一种修改）
                                    isCoverImageModified = true
                                }) {
                                    Image(systemName: "minus.circle.fill")
                                        .font(.title3)
                                        .foregroundColor(.red)
                                        .background(Color.white)
                                        .clipShape(Circle())
                                }
                                .padding(6) // 控制右上角内边距
                            }
                    } else {
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(style: StrokeStyle(lineWidth: 1, dash: [6]))
                            .foregroundColor(Color.accentColor.opacity(0.5))
                            .frame(maxWidth: .infinity, minHeight: 160)
                            .overlay {
                                Image(systemName: "photo")
                                    .font(.system(size: 50))
                                    .foregroundColor(Color.accentColor.opacity(0.5))
                            }
                    }
                }
            }
        }
        
    }
    
    private var taskSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack() {
                Text("Collection Item")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(selectedTasks.count) items selected")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            
            selectedTasksPreview
                .shadow(radius: 10)
            
            // Available Tasks List
            LazyVStack(spacing: 8) {
                if collectionManager.allAchievements.isEmpty {
                    // Empty state
                    VStack(spacing: 16) {
                        Image(systemName: "music.note.list")
                            .font(.system(size: 60))
                            .foregroundColor(.secondary.opacity(0.6))

                        Text("No completed tasks yet")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 40)
                } else {
                    // Search Bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        
                        TextField("Search Piece Name or Composer Name", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    .padding()
                    .background(Color.accentColor.opacity(0.1))
                    .cornerRadius(10)
                    ForEach(filteredAchievements, id: \.id) { task in
                        TaskSelectionRow(
                            task: task,
                            isSelected: selectedTasks.contains(task)
                        ) {
                            toggleTaskSelection(task)
                        }
                    }
                }
            }
            .padding(.horizontal)
        }
    }
    
    private var defaultSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack() {
                Text("Collection Item")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            Text("Includes all completed tasks, automatically updated")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal)
    }
    
    private var selectedTasksPreview: some View {
//        VStack(alignment: .leading, spacing: 8) {
//            HStack {
//                Text("\(selectedTasks.count) items selected")
//                    .font(.subheadline)
//                    .fontWeight(.medium)
//                    .foregroundColor(.secondary)
//
//                Spacer()
//
////                TODO: implement long press to reorder
////                Text("Long press to reorder")
////                    .font(.caption)
////                    .foregroundColor(.gray)
//            }
//            .padding(.horizontal)

        ScrollView(.horizontal, showsIndicators: false) {
            HStack(alignment: .top, spacing: 12) {
                ForEach(selectedTasks.indices, id: \.self) { index in
                    SelectedTaskCard(
                        task: selectedTasks[index],
                        onRemove: {
                            selectedTasks.remove(at: index)
                        }
                    )
                    .padding(.top)
                }
            }
            .padding(.horizontal)
        }
    }
    
    private var filteredAchievements: [TaskItem] {
        let achievements = collectionManager.allAchievements
        if searchText.isEmpty {
            return achievements
        } else {
            return achievements.filter { task in
                task.pieceName.localizedCaseInsensitiveContains(searchText) ||
                task.composerName.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    // MARK: - Actions
    private func setupForEditing() {
        if let collection = editingCollection {
            collectionName = collection.name
            collectionDescription = collection.desc
            originalCoverImagePath = collection.cover
            selectedTasks = collection.taskItems ?? []

            // Load existing cover image
            if !collection.cover.isEmpty {
                Task {
                    coverImage = await imageManager.loadImageAsync(path: collection.cover)
                }
            }
        }
    }
    
    private func toggleTaskSelection(_ task: TaskItem) {
        if let index = selectedTasks.firstIndex(of: task) {
            selectedTasks.remove(at: index)
        } else {
            selectedTasks.append(task)
        }
    }

//    private func moveSelectedTasks(from source: IndexSet, to destination: Int) {
//        selectedTasks.move(fromOffsets: source, toOffset: destination)
//    }
 
    private func moveTask(from sourceIndex: Int, to destinationIndex: Int) {
        guard sourceIndex != destinationIndex,
              sourceIndex >= 0, sourceIndex < selectedTasks.count,
              destinationIndex >= 0, destinationIndex < selectedTasks.count else {
            return
        }

        let task = selectedTasks.remove(at: sourceIndex)
        selectedTasks.insert(task, at: destinationIndex)
    }
    

    
    private func saveCollection() {
        let trimmedName = collectionName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else { return }

        var finalCoverPath = originalCoverImagePath ?? ""

        // 只有当图片被修改时才处理图片保存
        if isCoverImageModified {
            // 删除原有图片文件（如果存在）
            if let originalPath = originalCoverImagePath, !originalPath.isEmpty {
                imageManager.deleteImage(path: originalPath)
            }

            // 保存新图片（如果有）
            if let image = coverImage {
                finalCoverPath = imageManager.saveCollectionCoverImage(image)
            } else {
                // 如果图片被删除，清空路径
                finalCoverPath = ""
            }
        }

        if let collection = editingCollection {
            // Update existing collection
            collectionManager.updateCollection(
                collection,
                name: trimmedName,
                desc: collectionDescription,
                cover: finalCoverPath,
                tasks: selectedTasks
            )
        } else {
            // Create new collection
            collectionManager.createCollection(
                name: trimmedName,
                desc: collectionDescription,
                cover: finalCoverPath,
                selectedTasks: selectedTasks
            )
        }

        // 如果图片被修改，触发全局图片刷新
        if isCoverImageModified {
            AppGlobalStateManager.shared.triggerImageRefresh()
        }

        dismiss()
    }

    private func cancelChanges() {
        // 如果有新选择的图片但还没保存，清理临时状态
        coverImage = nil
        selectedImage = nil
        isCoverImageModified = false
        dismiss()
    }
}

struct TaskSelectionRow: View {
    let task: TaskItem
    let isSelected: Bool
    let showDragHandle: Bool
    let onToggle: () -> Void

    @EnvironmentObject private var imageManager: ImageManager

    init(task: TaskItem, isSelected: Bool, showDragHandle: Bool = false, onToggle: @escaping () -> Void) {
        self.task = task
        self.isSelected = isSelected
        self.showDragHandle = showDragHandle
        self.onToggle = onToggle
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // Drag handle (only show when in reorder mode)
            // if showDragHandle {
            //     Image(systemName: "line.3.horizontal")
            //         .font(.title2)
            //         .foregroundColor(.secondary)
            //         .frame(width: 24)
            // }
            
            // Task Image
            Group {
                if let coverPath = task.coverImagePath {
                    CloudImageView(path: coverPath)
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 50, height: 50)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                } else {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.tertiarySystemBackground))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: "music.note")
                                .foregroundColor(.secondary)
                        )
                }
            }
            
            // Task Info
            VStack(alignment: .leading, spacing: 4) {
                Text(task.pieceName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(task.composerName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: isSelected ? "checkmark.circle" : "circle")
                .font(.title2)
                .foregroundColor(.green)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onToggle()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(8)
    }
}

// Keep the original SelectedTaskCard for backward compatibility
struct SelectedTaskCard: View {
    let task: TaskItem
    let onRemove: () -> Void

    @EnvironmentObject private var imageManager: ImageManager

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack(alignment: .topTrailing) {
                // Task Image
                Group {
                    if let coverPath = task.coverImagePath {
                        CloudImageView(path: coverPath)
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 80)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    } else {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.tertiarySystemBackground))
                            .frame(width: 80, height: 80)
                            .overlay(
                                Image(systemName: "music.note")
                                    .foregroundColor(.secondary)
                            )
                    }
                }

                // Remove Button
                Button(action: onRemove) {
                    Image(systemName: "minus.circle.fill")
                        .font(.title3)
                        .foregroundColor(.red)
                        .background(Color.white)
                        .clipShape(Circle())
                }
                .offset(x: 8, y: -8)
            }

            // Task Name
            Text(task.pieceName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
                .frame(width: 80)
        }
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container.mainContext

    modelContext.insert(TaskItem(pieceName: "Chopin Etude Chopin Etude", composerName: "Chopin", key: .aFlatMajor, difficulty: .eight, beginDate: .now, taskType: .Achievement))
    
    return CreateCollectionView()
        .environmentObject(CollectionManager.shared)
        .environmentObject(ImageManager(modelContext: modelContext))
}
