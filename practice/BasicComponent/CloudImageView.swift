//
//  CloudImageView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/13.
//

import SwiftUI

struct CloudImageView: View {
    let path: String
    @EnvironmentObject var imageManager: ImageManager
    @State private var image: UIImage?
    var refreshTrigger: String = ""
    
    var body: some View {
        Group {
            if let image = image {
                Image(uiImage: image)
                    .resizable()
            } else {
                ProgressView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.clear)
                    .contentShape(Rectangle())
            }
        }
        .task(id: refreshTrigger) {
            self.image = await imageManager.loadImageAsync(path: path)
        }
    }
}
